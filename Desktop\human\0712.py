import sys
sys.path.append("..")

import cv2
import numpy as np
import pupil_apriltags as apriltag  # 使用pupil_apriltags库
from uptech import UpTech
import time
import random

"""
仿人散打机器人控制程序 - 边缘策略增强版
核心策略：
1. 边界检测：使用灰度传感器检测擂台边缘，避免自己掉下擂台
2. 边缘利用策略：机器人后方可接近边缘依托围栏，增强稳定性和防守能力
3. 面向中心策略：在边缘时确保机器人面向擂台中心，利用摄像头识别敌方二维码
4. 防反弹机制：当敌人靠在围栏边时，降低攻击力度并快速检测反弹
5. 智能攻击策略：检测敌人是否靠边推不动，采用侧面攻击或拉拽策略
6. 边缘防守反击：当自己后方靠边时，利用围栏支撑进行稳定的防守反击
7. 连续攻击监控：记录连续攻击次数，判断是否需要改变策略
8. 实时姿态调整：攻击后立即检测倾角，如有不稳定立即调整

适用于1.2m×1.2m擂台，50cm高围栏的散打比赛环境
# 可根据实际测试调整的关键参数：
self.edge_threshold_low = 1000    # 边界检测阈值
self.edge_threshold_high = 3000   # 边界检测阈值
self.consecutive_attacks >= 2     # 连续攻击判断阈值
position_change < 0.1            # 敌人位置变化阈值
size > 80                        # 敌人距离判断阈值
"""

class RobotFighter:
    def __init__(self):
        # 初始化硬件
        self.uptech = UpTech()

        # 初始化硬件模块
        self.uptech.ADC_IO_Open()
        self.uptech.CDS_Open()
        self.uptech.Camera_Open()

        # 初始化AprilTag检测器
        self.detector = apriltag.Detector(families='tag36h11')
        
        # 配置舵机
        # 左右轮
        self.uptech.CDS_SetMode(1, 0)  # 左轮
        self.uptech.CDS_SetMode(2, 0)  # 右轮
        
        # 左胳膊的3个舵机编号分别为4、5、6
        self.uptech.CDS_SetMode(4, 0)
        self.uptech.CDS_SetMode(5, 0)
        self.uptech.CDS_SetMode(6, 0)
        
        # 右胳膊的3个舵机编号分别为7、8、9
        self.uptech.CDS_SetMode(7, 0)  
        self.uptech.CDS_SetMode(8, 0)     
        self.uptech.CDS_SetMode(9, 0)
        
        # 状态变量
        self.forward_fall_count = 0  # 前倾计数
        self.backward_fall_count = 0  # 后倾计数
        self.search_count = 0  # 搜索计数
        self.recovery_attempts = 0  # 恢复尝试次数
        self.last_attack_time = time.time()  # 上次攻击时间
        self.consecutive_attacks = 0  # 连续攻击次数
        self.last_enemy_position = None  # 上次敌人位置
        
        # 倾角传感器阈值
        self.angle_normal_min = 1428
        self.angle_normal_max = 2628
        
        # 边界检测阈值 (灰度传感器值，需要根据实际情况调整)
        self.edge_threshold_low = 1000   # 低于此值认为接近边界
        self.edge_threshold_high = 3000  # 高于此值认为接近边界
        
        # 初始化完成后，执行默认姿势
        self.default_pose()
        print("机器人初始化完成")
    
    #---------------------------传感器相关函数---------------------------
    
    def get_tilt_angle(self):
        """
        获取倾角传感器值
        返回值:
        1 - 正常站立
        2 - 前倾
        3 - 后倾
        0 - 不确定状态
        """
        angle_value = self.uptech.ADC_Get_Channel(2)  # 使用通道2作为倾角传感器
        print(f"倾角传感器值: {angle_value}")
        
        # 正常站立
        if self.angle_normal_min < angle_value < self.angle_normal_max:
            return 1
        # 前倾
        elif angle_value <= self.angle_normal_min:
            return 2
        # 后倾
        elif angle_value >= self.angle_normal_max:
            return 3
        # 不确定状态
        else:
            return 0
    
    def get_gray_sensors(self):
        """获取灰度传感器值"""
        front_gray = self.uptech.ADC_Get_Channel(0)  # 前灰度传感器
        back_gray = self.uptech.ADC_Get_Channel(1)   # 后灰度传感器
        return front_gray, back_gray
    
    def is_near_edge(self):
        """
        检测是否接近擂台边缘
        返回值:
        0 - 安全区域
        1 - 前方接近边缘
        2 - 后方接近边缘
        3 - 前后都接近边缘
        """
        front_gray, back_gray = self.get_gray_sensors()
        print(f"灰度传感器值 - 前: {front_gray}, 后: {back_gray}")
        
        front_edge = (front_gray < self.edge_threshold_low or front_gray > self.edge_threshold_high)
        back_edge = (back_gray < self.edge_threshold_low or back_gray > self.edge_threshold_high)
        
        if front_edge and back_edge:
            return 3  # 前后都接近边缘
        elif front_edge:
            return 1  # 前方接近边缘
        elif back_edge:
            return 2  # 后方接近边缘
        else:
            return 0  # 安全区域
    
    def check_enemy_near_edge(self, enemy_info):
        """
        检查敌人是否靠近边缘（可能推不动）
        基于敌人位置和连续攻击次数判断
        """
        tag_id, position = enemy_info
        
        if tag_id == 0 or not position:
            return False
            
        # 如果敌人位置没有明显变化且连续攻击多次，可能靠边了
        if self.last_enemy_position:
            last_x, last_y, _ = self.last_enemy_position  # 不使用last_size
            x, y, size = position
            
            # 位置变化很小且敌人距离很近（size大）
            position_change = abs(x - last_x) + abs(y - last_y)
            if position_change < 0.1 and size > 80 and self.consecutive_attacks >= 2:
                print("检测到敌人可能靠边，推不动")
                return True
                
        return False
    
    def detect_apriltags(self):
        """检测画面中的AprilTag标签"""
        try:
            frame = self.uptech.Camera_Get_Image()
            if frame is None:
                print("获取摄像头图像失败")
                return None

            # 转换为灰度图
            gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)

            # 检测AprilTag
            results = self.detector.detect(gray)

            if not results:
                return None

            if ids is None or len(ids) == 0:
                return None

            # 返回检测到的标签信息
            detected_tags = []
            for i, tag_id in enumerate(ids.flatten()):
                corner = corners[i][0]  # 获取角点坐标

                # 计算标签中心点
                center_x = int(np.mean(corner[:, 0]))
                center_y = int(np.mean(corner[:, 1]))
                center = (center_x, center_y)

                # 计算标签大小作为距离的估计
                size = np.mean([
                    np.linalg.norm(corner[0] - corner[1]),
                    np.linalg.norm(corner[1] - corner[2]),
                    np.linalg.norm(corner[2] - corner[3]),
                    np.linalg.norm(corner[3] - corner[0])
                ])

                detected_tags.append({
                    'id': int(tag_id),
                    'center': center,
                    'size': size,
                    'frame_width': frame.shape[1],
                    'frame_height': frame.shape[0]
                })

                print(f"检测到AprilTag ID: {tag_id}, 中心: {center}, 大小: {size:.2f}")

            return detected_tags

        except Exception as e:
            print(f"AprilTag检测错误: {e}")
            return None


    
    def get_enemy_position(self):
        """
        获取敌人位置信息
        返回值:
        (0, None) - 未检测到敌人
        (tag_id, position) - 敌人ID和相对位置
            tag_id: 1-前方, 2-左侧, 3-右侧, 4-后方
            position: 相对位置 (x, y, size)，x和y是标签中心在图像中的位置，size是标签大小
        """
        tags = self.detect_apriltags()
        if not tags:
            return (0, None)
            
        # 如果检测到多个标签，选择最大的一个（最近的）
        largest_tag = max(tags, key=lambda x: x['size'])
        tag_id = largest_tag['id']
        
        # 计算标签在图像中的相对位置
        frame_width = largest_tag['frame_width']
        frame_height = largest_tag['frame_height']
        center_x = largest_tag['center'][0] / frame_width  # 归一化到0-1
        center_y = largest_tag['center'][1] / frame_height  # 归一化到0-1
        
        return (tag_id, (center_x, center_y, largest_tag['size']))
    
    #---------------------------运动控制函数---------------------------
    
    def move(self, left_speed, right_speed, duration=0):
        """
        控制机器人移动
        left_speed: 左轮速度
        right_speed: 右轮速度
        duration: 持续时间，为0时不等待
        """
        self.uptech.CDS_SetSpeed(1, left_speed)
        self.uptech.CDS_SetSpeed(2, -right_speed)
        
        if duration > 0:
            time.sleep(duration)
            self.stop()
    
    def stop(self):
        """停止移动"""
        self.uptech.CDS_SetSpeed(1, 0)
        self.uptech.CDS_SetSpeed(2, 0)
    
    def default_pose(self):
        """默认站立姿势"""
        self.uptech.CDS_SetAngle(4, 512, 512)  # 左肩
        self.uptech.CDS_SetAngle(5, 512, 512)  # 左肘
        self.uptech.CDS_SetAngle(6, 512, 512)  # 左腕
        self.uptech.CDS_SetAngle(7, 512, 512)  # 右肩
        self.uptech.CDS_SetAngle(8, 512, 512)  # 右肘
        self.uptech.CDS_SetAngle(9, 512, 512)  # 右腕
        time.sleep(0.5)
    
    def start_pose(self):
        """上台前的准备姿势"""
        # 摆个Pose
        self.uptech.CDS_SetAngle(4, 700, 512)  # 左肩前伸 (4号数值增大=向前)
        self.uptech.CDS_SetAngle(5, 400, 512)  # 左肘向上 (5号数值减小=向上)
        self.uptech.CDS_SetAngle(6, 400, 512)  # 左腕向上 (6号数值减小=向上)
        self.uptech.CDS_SetAngle(7, 300, 512)  # 右肩前伸 (7号数值减小=向前)
        self.uptech.CDS_SetAngle(8, 400, 512)  # 右肘向上 (8号数值减小=向上)
        self.uptech.CDS_SetAngle(9, 400, 512)  # 右腕向上 (9号数值减小=向上)
        time.sleep(0.3)

    def stand_up_forward(self):
        """前倾倒后站起"""
        # 第一步：双臂前伸
        self.uptech.CDS_SetAngle(4, 700, 512)  # 左肩前伸 (4号数值增大=向前)
        self.uptech.CDS_SetAngle(5, 600, 512)  # 左肘向下支撑 (5号数值增大=向下)
        self.uptech.CDS_SetAngle(6, 600, 512)  # 左腕向下支撑 (6号数值增大=向下)
        self.uptech.CDS_SetAngle(7, 300, 512)  # 右肩前伸 (7号数值减小=向前)
        self.uptech.CDS_SetAngle(8, 600, 512)  # 右肘向下支撑 (8号数值增大=向下)
        self.uptech.CDS_SetAngle(9, 600, 512)  # 右腕向下支撑 (9号数值增大=向下)
        time.sleep(1.0)

        # 第二步：支撑身体
        self.uptech.CDS_SetAngle(4, 800, 800)  # 左肩更前伸用力 (4号数值增大=向前)
        self.uptech.CDS_SetAngle(7, 200, 800)  # 右肩更前伸用力 (7号数值减小=向前)
        time.sleep(1.0)

        # 第三步：抬起上身
        self.uptech.CDS_SetAngle(4, 350, 512)  # 左肩稍微后收
        self.uptech.CDS_SetAngle(5, 450, 512)  # 左肘向上 (5号数值减小=向上)
        self.uptech.CDS_SetAngle(7, 350, 512)  # 右肩稍微后收
        self.uptech.CDS_SetAngle(8, 450, 512)  # 右肘向上 (8号数值减小=向上)
        time.sleep(0.8)

        # 第四步：恢复站立
        self.default_pose()

        # 向后移动一点以稳定身体
        self.move(-300, -300, 0.3)

    def stand_up_backward(self):
        """后倾倒后站起"""
        # 第一步：双臂后伸
        self.uptech.CDS_SetAngle(4, 300, 512)  # 左肩后伸 (4号数值减小=向后)
        self.uptech.CDS_SetAngle(5, 600, 512)  # 左肘向下支撑 (5号数值增大=向下)
        self.uptech.CDS_SetAngle(6, 600, 512)  # 左腕向下支撑 (6号数值增大=向下)
        self.uptech.CDS_SetAngle(7, 700, 512)  # 右肩后伸 (7号数值增大=向后)
        self.uptech.CDS_SetAngle(8, 600, 512)  # 右肘向下支撑 (8号数值增大=向下)
        self.uptech.CDS_SetAngle(9, 600, 512)  # 右腕向下支撑 (9号数值增大=向下)
        time.sleep(1.0)

        # 第二步：支撑身体
        self.uptech.CDS_SetAngle(4, 200, 800)  # 左肩更后伸用力 (4号数值减小=向后)
        self.uptech.CDS_SetAngle(7, 800, 800)  # 右肩更后伸用力 (7号数值增大=向后)
        time.sleep(1.0)

        # 第三步：抬起上身
        self.uptech.CDS_SetAngle(4, 650, 512)  # 左肩稍微前收
        self.uptech.CDS_SetAngle(5, 450, 512)  # 左肘向上 (5号数值减小=向上)
        self.uptech.CDS_SetAngle(7, 650, 512)  # 右肩稍微前收
        self.uptech.CDS_SetAngle(8, 450, 512)  # 右肘向上 (8号数值减小=向上)
        time.sleep(0.8)

        # 第四步：恢复站立
        self.default_pose()

        # 向前移动一点以稳定身体
        self.move(300, 300, 0.3)

    def attack_front(self):
        """正面攻击动作 - 带防反弹机制"""
        # 准备姿势
        self.uptech.CDS_SetAngle(4, 600, 512)  # 左肩微前 (4号数值增大=向前)
        self.uptech.CDS_SetAngle(5, 450, 512)  # 左肘向上准备 (5号数值减小=向上)
        self.uptech.CDS_SetAngle(6, 450, 512)  # 左腕向上准备 (6号数值减小=向上)
        self.uptech.CDS_SetAngle(7, 400, 512)  # 右肩微前 (7号数值减小=向前)
        self.uptech.CDS_SetAngle(8, 450, 512)  # 右肘向上准备 (8号数值减小=向上)
        self.uptech.CDS_SetAngle(9, 450, 512)  # 右腕向上准备 (9号数值减小=向上)
        time.sleep(0.3)

        # 快速推出 - 降低力度避免反弹
        self.uptech.CDS_SetAngle(4, 700, 800)   # 左肩前伸 (降低力度)
        self.uptech.CDS_SetAngle(5, 580, 800)   # 左肘向下推出 (降低力度)
        self.uptech.CDS_SetAngle(7, 300, 800)   # 右肩前伸 (降低力度)
        self.uptech.CDS_SetAngle(8, 580, 800)   # 右肘向下推出 (降低力度)

        # 短促向前冲 - 减少冲击时间
        self.move(400, 400, 0.4)

        # 立即检查倾角状态，如果有反弹迹象立即后退
        time.sleep(0.1)
        tilt_state = self.get_tilt_angle()
        if tilt_state == 2:  # 检测到前倾（可能是反弹）
            print("检测到反弹，立即后退")
            self.move(-300, -300, 0.3)

        # 恢复默认姿势
        self.default_pose()

        # 更新上次攻击时间
        self.last_attack_time = time.time()

    def attack_left(self):
        """左侧攻击动作 - 带防反弹机制"""
        # 左转
        self.move(-300, 300, 0.4)

        # 左臂攻击 - 降低力度
        self.uptech.CDS_SetAngle(4, 700, 700)  # 左肩前伸 (降低力度)
        self.uptech.CDS_SetAngle(5, 580, 700)  # 左肘向下推出 (降低力度)
        self.uptech.CDS_SetAngle(6, 580, 512)  # 左腕向下推出 (降低力度)
        time.sleep(0.3)

        # 向左前方冲 - 减少冲击
        self.move(350, 250, 0.4)

        # 检查反弹并快速调整
        time.sleep(0.1)
        tilt_state = self.get_tilt_angle()
        if tilt_state != 1:  # 如果不是正常站立
            print("左侧攻击后检测到不稳定，调整姿态")
            self.move(-200, -200, 0.2)

        # 恢复默认姿势
        self.default_pose()

        # 更新上次攻击时间
        self.last_attack_time = time.time()

    def attack_right(self):
        """右侧攻击动作 - 带防反弹机制"""
        # 右转
        self.move(300, -300, 0.4)

        # 右臂攻击 - 降低力度
        self.uptech.CDS_SetAngle(7, 300, 700)  # 右肩前伸 (降低力度)
        self.uptech.CDS_SetAngle(8, 580, 700)  # 右肘向下推出 (降低力度)
        self.uptech.CDS_SetAngle(9, 580, 512)  # 右腕向下推出 (降低力度)
        time.sleep(0.3)

        # 向右前方冲 - 减少冲击
        self.move(250, 350, 0.4)

        # 检查反弹并快速调整
        time.sleep(0.1)
        tilt_state = self.get_tilt_angle()
        if tilt_state != 1:  # 如果不是正常站立
            print("右侧攻击后检测到不稳定，调整姿态")
            self.move(-200, -200, 0.2)

        # 恢复默认姿势
        self.default_pose()

        # 更新上次攻击时间
        self.last_attack_time = time.time()

    def attack_back(self):
        """后方攻击动作"""
        # 转身180度
        self.move(400, -400, 1.0)

        # 执行前方攻击
        self.attack_front()
    
    def side_attack_strategy(self, enemy_info):
        """
        侧面攻击策略 - 当敌人靠边推不动时使用
        通过侧面攻击避免正面反弹
        """
        # 解包敌人信息（虽然这里主要用于策略选择，不直接使用位置）
        _, _ = enemy_info
        
        print("敌人靠边，采用侧面攻击策略")
        
        # 随机选择左侧或右侧攻击
        import random
        side = random.choice(['left', 'right'])
        
        if side == 'left':
            # 向左移动后攻击
            print("执行左侧迂回攻击")
            self.move(-200, 200, 0.6)  # 左转
            time.sleep(0.2)
            self.move(300, 300, 0.4)   # 前进
            self.attack_left()
        else:
            # 向右移动后攻击
            print("执行右侧迂回攻击")
            self.move(200, -200, 0.6)  # 右转
            time.sleep(0.2)
            self.move(300, 300, 0.4)   # 前进
            self.attack_right()
        
        # 攻击后快速后退避免反弹
        self.move(-250, -250, 0.3)
        
        # 重置连续攻击计数
        self.consecutive_attacks = 0
    
    def pull_attack_strategy(self):
        """
        拉拽攻击策略 - 尝试将敌人拉离边缘
        """
        print("尝试拉拽攻击")

        # 双臂向内收拢准备拉拽
        self.uptech.CDS_SetAngle(4, 400, 512)  # 左肩内收
        self.uptech.CDS_SetAngle(5, 400, 512)  # 左肘向上
        self.uptech.CDS_SetAngle(6, 400, 512)  # 左腕向上
        self.uptech.CDS_SetAngle(7, 600, 512)  # 右肩内收
        self.uptech.CDS_SetAngle(8, 400, 512)  # 右肘向上
        self.uptech.CDS_SetAngle(9, 400, 512)  # 右腕向上
        time.sleep(0.3)

        # 快速前进接近
        self.move(400, 400, 0.3)

        # 向后拉拽动作
        self.uptech.CDS_SetAngle(4, 300, 800)  # 左肩后拉
        self.uptech.CDS_SetAngle(7, 700, 800)  # 右肩后拉

        # 同时后退
        self.move(-400, -400, 0.5)

        # 恢复默认姿势
        self.default_pose()

        # 重置连续攻击计数
        self.consecutive_attacks = 0

    def edge_defense_attack(self):
        """
        边缘防守反击策略 - 当自己后方靠边时的专用攻击
        利用围栏作为支撑，进行稳定的防守反击
        """
        print("执行边缘防守反击")

        # 稍微前倾，准备迎击，利用围栏支撑
        self.uptech.CDS_SetAngle(4, 600, 512)  # 左肩微前
        self.uptech.CDS_SetAngle(5, 480, 512)  # 左肘稍下，准备防守
        self.uptech.CDS_SetAngle(6, 480, 512)  # 左腕稍下
        self.uptech.CDS_SetAngle(7, 400, 512)  # 右肩微前
        self.uptech.CDS_SetAngle(8, 480, 512)  # 右肘稍下，准备防守
        self.uptech.CDS_SetAngle(9, 480, 512)  # 右腕稍下
        time.sleep(0.2)

        # 稳定的推击，力度适中，避免自己失衡
        self.uptech.CDS_SetAngle(4, 650, 600)   # 左肩推出（力度适中）
        self.uptech.CDS_SetAngle(5, 550, 600)   # 左肘推出
        self.uptech.CDS_SetAngle(7, 350, 600)   # 右肩推出（力度适中）
        self.uptech.CDS_SetAngle(8, 550, 600)   # 右肘推出

        # 短促前进，利用围栏支撑
        self.move(300, 300, 0.3)

        # 立即检查状态，如果稳定则继续，不稳定则调整
        time.sleep(0.1)
        tilt_state = self.get_tilt_angle()
        if tilt_state != 1:
            print("边缘攻击后调整姿态")
            # 轻微后靠，利用围栏稳定
            self.move(-100, -100, 0.1)

        # 恢复防守姿势
        self.default_pose()

        # 更新攻击时间
        self.last_attack_time = time.time()

    def search_rotate(self, direction=1):
        """
        搜索旋转
        direction: 1为顺时针，-1为逆时针
        """
        speed = 300
        self.move(speed * direction, -speed * direction, 0.05)

    def ensure_facing_center(self):
        """
        确保机器人面向擂台中心
        当机器人在边缘时调用此函数
        """
        edge_status = self.is_near_edge()

        if edge_status == 2:  # 后方接近边缘
            print("后方靠边，确保面向擂台中心")
            # 后方靠边时，应该面向前方（擂台中心）
            # 检查是否需要调整方向，通过小幅度转动来寻找最佳角度

            # 尝试检测前方是否有足够的空间（通过前灰度传感器）
            front_gray, _ = self.get_gray_sensors()

            # 如果前方传感器显示安全，说明面向中心方向正确
            if self.edge_threshold_low < front_gray < self.edge_threshold_high:
                print("已正确面向擂台中心")
                return True
            else:
                print("调整方向以面向擂台中心")
                # 小幅度左右调整，寻找面向中心的角度
                self.move(100, -100, 0.2)  # 轻微右转
                time.sleep(0.1)

                # 再次检查
                front_gray, _ = self.get_gray_sensors()
                if self.edge_threshold_low < front_gray < self.edge_threshold_high:
                    return True
                else:
                    # 尝试左转
                    self.move(-200, 200, 0.4)  # 左转回来并继续
                    return True

        elif edge_status == 1:  # 前方接近边缘
            print("前方接近边缘，转向面向中心")
            # 前方接近边缘，需要转身面向中心
            self.move(200, -200, 0.5)  # 右转90度左右
            return True

        return False
    
    #---------------------------行为处理函数---------------------------
    
    def handle_falling(self, tilt_state):
        """处理倒下状态"""
        # 前倾
        if tilt_state == 2:
            self.forward_fall_count += 1
            if self.forward_fall_count >= 5:
                print("检测到前倾倒，尝试恢复站立")
                self.stop()
                time.sleep(0.1)
                
                # 执行前倾恢复动作
                self.stand_up_forward()
                time.sleep(0.5)
                
                # 检查是否恢复成功
                if self.get_tilt_angle() == 1:
                    print("成功恢复站立")
                    self.forward_fall_count = 0
                    self.recovery_attempts = 0
                else:
                    print("恢复失败，再次尝试")
                    self.recovery_attempts += 1
                    # 如果多次尝试失败，尝试不同的策略
                    if self.recovery_attempts > 3:
                        print("多次恢复失败，尝试其他策略")
                        self.move(200, 200, 0.3)
                        self.recovery_attempts = 0
            else:
                time.sleep(0.05)
        
        # 后倾
        elif tilt_state == 3:
            self.backward_fall_count += 1
            if self.backward_fall_count >= 5:
                print("检测到后倾倒，尝试恢复站立")
                self.stop()
                time.sleep(0.1)
                
                # 执行后倾恢复动作
                self.stand_up_backward()
                time.sleep(0.5)
                
                # 检查是否恢复成功
                if self.get_tilt_angle() == 1:
                    print("成功恢复站立")
                    self.backward_fall_count = 0
                    self.recovery_attempts = 0
                else:
                    print("恢复失败，再次尝试")
                    self.recovery_attempts += 1
                    # 如果多次尝试失败，尝试不同的策略
                    if self.recovery_attempts > 3:
                        print("多次恢复失败，尝试其他策略")
                        self.move(-200, -200, 0.3)
                        self.recovery_attempts = 0
            else:
                time.sleep(0.05)
        
        # 不确定状态
        else:
            print("倾倒状态不明确，尝试轻微移动")
            self.move(150, 150, 0.2)
    
    def handle_enemy(self, enemy_info):
        """处理敌人信息"""
        tag_id, position = enemy_info

        # 攻击冷却时间检查 - 防止连续攻击
        current_time = time.time()
        if current_time - self.last_attack_time < 1.5:  # 1.5秒冷却时间
            self.search_rotate()
            return

        # 未检测到敌人
        if tag_id == 0:
            self.search_enemy()
            self.consecutive_attacks = 0  # 重置连续攻击计数
            return

        # 检查自己是否接近边缘
        edge_status = self.is_near_edge()
        if edge_status != 0:
            print(f"警告：接近边缘 (状态: {edge_status})，采用边缘策略")
            # 边缘策略优化
            if edge_status == 1:  # 前方接近边缘
                print("前方接近边缘，后退并调整面向中心")
                self.move(-200, -200, 0.3)
                # 轻微转向确保面向擂台中心
                self.move(100, -100, 0.2)
                return
            elif edge_status == 2:  # 后方接近边缘 - 利用围栏策略
                print("后方接近边缘，依托围栏防守，调整面向中心")
                # 不后退，而是调整方向确保面向擂台中心
                # 如果检测到敌人，可以利用围栏进行防守反击
                if tag_id != 0:
                    print("后靠围栏，准备防守反击")
                    # 轻微前倾准备迎击
                    self.move(50, 50, 0.1)
                    # 继续处理敌人，不return，让后续攻击逻辑处理
                else:
                    # 没有敌人时，调整面向中心
                    self.move(100, -100, 0.2)
                    return
            else:  # 前后都接近边缘
                print("前后都接近边缘，调整到安全位置")
                self.move(150, -150, 0.3)  # 转向移动到安全区域
                return
        
        # 检查敌人是否靠边推不动
        enemy_near_edge = self.check_enemy_near_edge(enemy_info)
        
        # 更新敌人位置记录
        self.last_enemy_position = position
        
        # 根据标签ID确定敌人方向和攻击策略
        if tag_id == 1:  # 前方
            print("敌人在前方")

            # 检查自己是否后方靠边，如果是则使用边缘防守反击
            if edge_status == 2:  # 后方接近边缘
                print("后方靠边，使用边缘防守反击策略")
                self.edge_defense_attack()
                self.consecutive_attacks += 1
                return

            # 如果敌人靠边推不动，使用特殊策略
            if enemy_near_edge:
                # 随机选择策略
                import random
                strategy = random.choice(['side', 'pull'])
                if strategy == 'side':
                    self.side_attack_strategy(enemy_info)
                else:
                    self.pull_attack_strategy()
                return

            # 正常攻击
            print("发起正面攻击")
            # 如果有位置信息，根据位置微调方向
            if position:
                x, _, _ = position  # 只使用x坐标进行方向调整
                if x < 0.4:  # 敌人偏左
                    self.move(-150, 150, 0.1)  # 微调左转
                elif x > 0.6:  # 敌人偏右
                    self.move(150, -150, 0.1)  # 微调右转

            # 攻击
            self.attack_front()
            self.consecutive_attacks += 1
            
        elif tag_id == 2:  # 左侧
            print("敌人在左侧，左转攻击")
            self.attack_left()
            self.consecutive_attacks += 1
            
        elif tag_id == 3:  # 右侧
            print("敌人在右侧，右转攻击")
            self.attack_right()
            self.consecutive_attacks += 1
            
        elif tag_id == 4:  # 后方
            print("敌人在后方，转身攻击")
            self.attack_back()
            self.consecutive_attacks += 1
    
    def search_enemy(self):
        """搜索敌人"""
        self.search_count += 1

        # 首先检查是否在边缘，如果在边缘优先调整面向中心
        edge_status = self.is_near_edge()
        if edge_status != 0:
            print("在边缘位置搜索敌人，优先确保面向中心")
            if self.ensure_facing_center():
                # 面向中心后，进行有限的搜索旋转
                self.search_rotate()
                return

        # 每20次循环改变一次搜索策略
        if self.search_count >= 20:
            self.search_count = 0

            # 如果在边缘，使用更保守的搜索策略
            if edge_status != 0:
                print("边缘位置，使用保守搜索策略")
                # 只进行原地旋转，不移动
                turn_dir = random.choice([-1, 1])
                self.move(200 * turn_dir, -200 * turn_dir, 0.5)
            else:
                # 正常搜索策略
                strategy = random.randint(1, 4)

                if strategy == 1:
                    # 原地旋转
                    print("搜索策略：原地旋转")
                    turn_dir = random.choice([-1, 1])
                    self.move(300 * turn_dir, -300 * turn_dir, 0.8)

                elif strategy == 2:
                    # 前进一段距离
                    print("搜索策略：前进")
                    self.move(300, 300, 0.6)

                elif strategy == 3:
                    # 弧线移动
                    print("搜索策略：弧线移动")
                    self.move(350, 250, 0.7)

                else:
                    # 随机转向
                    print("搜索策略：随机转向")
                    turn_angle = random.uniform(0.3, 1.0)
                    turn_dir = random.choice([-1, 1])
                    self.move(300 * turn_dir, -300 * turn_dir, turn_angle)

            # 恢复默认姿势
            self.default_pose()

        else:
            # 缓慢旋转寻找敌人
            self.search_rotate()
    
    #---------------------------主循环---------------------------
    
    def run(self):
        """主循环"""
        try:
            print("机器人开始运行...")
            
            # 开始姿势
            self.start_pose()
            time.sleep(1.0)
            print("等待上台...")
            
            # 上台动作
            self.move(300, 300, 2.0)
            self.default_pose()
            time.sleep(0.5)
            
            print("开始比赛...")
            
            while True:
                # 1. 检测倾角状态
                tilt_state = self.get_tilt_angle()
                
                # 2. 如果倒下，执行恢复动作
                if tilt_state != 1:
                    self.handle_falling(tilt_state)
                    continue
                
                # 3. 检测敌人位置
                enemy_info = self.get_enemy_position()
                self.handle_enemy(enemy_info)
                
                # 短暂休眠以节省资源
                time.sleep(0.05)
                
        except KeyboardInterrupt:
            print("程序被用户中断")
            self.stop()
            self.default_pose()
        except Exception as e:
            print(f"程序发生错误: {e}")
            self.stop()
            self.default_pose()

# 主程序入口
if __name__ == "__main__":
    print("仿人散打机器人启动中...")
    robot = RobotFighter()
    
    try:
        robot.run()
    except Exception as e:
        print(f"程序发生错误: {e}")
    finally:
        print("程序结束")