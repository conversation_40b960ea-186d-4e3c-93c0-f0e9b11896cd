
from motion_controller import MotionController
from edge_detector import EdgeDetector
import time

motion = MotionController()
edge_detector = EdgeDetector()

nq = 0
nh = 0

while True:
    angle_state = edge_detector.angle_detect()
    print(angle_state)
    # 前倾
    if angle_state == 2:
        nq += 1
        if nq == 6:
            motion.move_cmd(0, 0)
            time.sleep(0.1)
            motion.default_action()
            time.sleep(0.8)
            motion.ahead_dump()
            time.sleep(0.5)
            nq = 0
        else:
            time.sleep(0.04)
                    
    # 后倾
    if angle_state == 3:
        nh += 1
        if nh == 6:
            motion.move_cmd(0, 0)
            time.sleep(0.1)
            motion.default_action()
            time.sleep(0.8)
            motion.behind_dump()
            time.sleep(0.5)
            nh = 0
        else:
            time.sleep(0.05)
    
    time.sleep(0.05)