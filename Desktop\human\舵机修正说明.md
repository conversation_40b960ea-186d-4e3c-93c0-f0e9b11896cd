# 舵机控制修正说明

## 舵机运动规律
根据您提供的实际测试结果：
- **4号舵机**：数值增大，舵机向前转
- **5号舵机**：数值增大，舵机向下转
- **6号舵机**：数值增大，舵机向下转
- **7号舵机**：数值增大，舵机向后转
- **8号舵机**：数值增大，舵机向下转
- **9号舵机**：数值增大，舵机向下转

## 主要修正内容

### 1. start_pose() 函数
- **4号舵机**：从300改为700（向前伸展）
- **5、6、8、9号舵机**：从原来的较大值改为400（向上准备姿势）
- **7号舵机**：保持300（向前伸展）

### 2. stand_up_forward() 函数（前倾恢复）
- **4号舵机**：改为700（向前伸展支撑）
- **7号舵机**：保持300（向前伸展支撑）
- **5、6、8、9号舵机**：设为600（向下支撑地面）
- 支撑阶段：4号设为800（更前伸），7号设为200（更前伸）

### 3. stand_up_backward() 函数（后倾恢复）
- **4号舵机**：设为300（向后伸展）
- **7号舵机**：设为700（向后伸展）
- **5、6、8、9号舵机**：设为600（向下支撑）
- 支撑阶段：4号设为200（更后伸），7号设为800（更后伸）

### 4. attack_front() 函数（正面攻击）
- 准备阶段：4号设为600（微前），7号保持400（微前）
- 攻击阶段：4号设为750（快速前伸），7号设为250（快速前伸）
- **5、6、8、9号舵机**：攻击时设为600（向下推出）

### 5. attack_left() 函数（左侧攻击）
- **4号舵机**：设为750（左臂前伸攻击）
- **5、6号舵机**：设为600（向下推出）

### 6. attack_right() 函数（右侧攻击）
- **7号舵机**：保持250（右臂前伸攻击）
- **8、9号舵机**：设为600（向下推出）

## 修正逻辑说明

### 前后运动控制
- **4号舵机（左肩）**：数值增大=向前，数值减小=向后
- **7号舵机（右肩）**：数值增大=向后，数值减小=向前
- 这两个舵机的运动方向相反，需要分别处理

### 上下运动控制
- **5、6、8、9号舵机**：数值增大=向下，数值减小=向上
- 攻击时需要向下推出（增大数值到600）
- 准备姿势时需要向上（减小数值到400-450）

## 测试建议
1. 先测试默认姿势是否正确
2. 测试准备姿势的手臂位置
3. 测试前倾和后倾恢复动作
4. 测试各方向的攻击动作
5. 根据实际效果微调数值

## 注意事项
- 所有数值都在200-800范围内，避免超出舵机限制
- 动作之间有适当的延时，确保舵机有足够时间完成动作
- 如果某个动作效果不理想，可以微调对应的数值
